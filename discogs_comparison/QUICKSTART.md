# Discogs API 客户端快速开始指南

## 🚀 快速安装

### 1. 安装依赖
```bash
pip install python3-discogs-client
```

### 2. 验证安装
```bash
python test_api_client.py
```

## 📋 基本使用

### 命令行使用

#### 获取单个 Release
```bash
python discogs_api_client.py --id 249504
```

#### 获取多个 Release
```bash
python discogs_api_client.py --ids 249504,1,2
```

#### 从文件读取 ID 列表
```bash
echo -e "249504\n1\n2" > release_ids.txt
python discogs_api_client.py --file release_ids.txt
```

#### 保存结果到文件
```bash
python discogs_api_client.py --id 249504 --output result.json
```

### 编程接口使用

```python
from discogs_api_client import ReleaseDataFetcher

# 创建获取器
fetcher = ReleaseDataFetcher()

# 获取单个 release
result = fetcher.fetch_single_release(249504)
if result.success:
    print(f"标题: {result.data['title']}")
else:
    print(f"错误: {result.error_message}")

# 批量获取
results = fetcher.fetch_batch_releases([249504, 1, 2])
for result in results:
    if result.success:
        print(f"Release {result.release_id}: {result.data['title']}")
```

## ⚠️ 重要提示

### 429 错误处理
如果遇到 "429: You are making requests too quickly" 错误：

1. **等待几分钟后重试**
2. **使用更长的延迟**：
   ```bash
   python discogs_api_client.py --id 249504 --rate-limit 2.0
   ```
3. **检查账号状态**：确认 Discogs token 有效

### 账号配置
脚本内置了两个测试账号，如需使用自己的账号：

```python
custom_accounts = [
    {
        'email': '<EMAIL>',
        'token': 'your_discogs_token',
        'user_agent': 'YourApp/1.0'
    }
]

fetcher = ReleaseDataFetcher(custom_accounts)
```

## 📊 数据格式

输出的数据格式与现有的 `api_release_补全器.py` 完全兼容：

```json
{
    "id": 249504,
    "title": "The Dark Side Of The Moon",
    "country": "UK",
    "year": 1973,
    "artists": [
        {
            "artist_id": 45467,
            "name": "Pink Floyd",
            "role": "Primary"
        }
    ],
    "genres": ["Rock"],
    "styles": ["Prog Rock", "Psychedelic Rock"],
    "formats": [...],
    "tracklist": [...],
    "created_at": "2025-08-03T...",
    "updated_at": "2025-08-03T..."
}
```

## 🔧 故障排除

### 常见问题

1. **ImportError: No module named 'discogs_client'**
   ```bash
   pip install python3-discogs-client
   ```

2. **429 错误（请求过于频繁）**
   - 等待几分钟后重试
   - 使用 `--rate-limit 2.0` 增加延迟

3. **认证失败**
   - 检查 Discogs token 是否有效
   - 确认账号没有被限制

### 调试模式
```bash
python discogs_api_client.py --id 249504 --verbose
```

## 📚 更多信息

- 详细文档：`README_discogs_api_client.md`
- 使用示例：`example_usage.py`
- 测试脚本：`test_api_client.py`

## 🎯 与现有流程集成

### 替换现有 API 调用
```python
# 原有代码
api_data = api_client.get_release(release_id)

# 新代码
fetcher = ReleaseDataFetcher()
result = fetcher.fetch_single_release(release_id)
if result.success:
    api_data = result.data
```

### 批量处理集成
```python
# 批量获取并处理
release_ids = [123456, 789012, 345678]
results = fetcher.fetch_batch_releases(release_ids)

for result in results:
    if result.success:
        # 处理成功的数据
        process_release_data(result.data)
    else:
        # 处理错误
        handle_error(result.release_id, result.error_message)
```

---

**🎉 现在您可以开始使用 Discogs API 客户端了！**

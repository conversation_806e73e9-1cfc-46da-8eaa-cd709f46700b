# 字段要求保存：

# 基础字段

doc = {
'id': DataConverter.safe_integer_value(release.id),
'title': DataConverter.safe_string_value(release.title, ''),
'country': DataConverter.safe_string_value(getattr(release, 'country', ''), ''),
'master_id': DataConverter.safe_integer_value(getattr(release, 'master', {}).get('id') if hasattr(release, 'master') and release.master else None),
'year': DataConverter.safe_integer_value(getattr(release, 'year', None)),
'notes': DataConverter.safe_string_value(getattr(release, 'notes', ''), ''),
'discogs_status': DataConverter.safe_string_value(getattr(release, 'status', ''), 'unknown'),

# 系统字段

'images_permissions': Permissions.ALL_VISIBLE,
'delete_status': Status.ACTIVE,
'permissions': Permissions.ALL_VISIBLE,
'source': Source.DISCOGS,
'created_at': datetime.now(timezone.utc),
'updated_at': datetime.now(timezone.utc)
}

# 处理艺术家

doc['artists'] = DataConverter.convert_artists(getattr(release, 'artists', []))
doc['extra_artists'] = DataConverter.convert_artists(getattr(release, 'extraartists', []))

# 处理标签和格式

doc['labels'] = DataConverter.convert_labels(getattr(release, 'labels', []))
doc['formats'] = DataConverter.convert_formats(getattr(release, 'formats', []))

# 处理分类

doc['genres'] = getattr(release, 'genres', [])
doc['styles'] = getattr(release, 'styles', [])

# 处理标识符和曲目

doc['identifiers'] = DataConverter.convert_identifiers(getattr(release, 'identifiers', []))
doc['tracklist'] = DataConverter.convert_tracklist(getattr(release, 'tracklist', []))

# 处理图片（优先从 release 表中获取，如果不存在则从 API 中获取）

doc['images'] = db_images if db_images is not None else []

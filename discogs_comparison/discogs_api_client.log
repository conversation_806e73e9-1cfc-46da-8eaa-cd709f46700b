2025-08-03 21:19:05,157 - INFO - 📋 准备获取 1 个Release数据
2025-08-03 21:19:05,162 - DEBUG - Starting new HTTPS connection (1): api.discogs.com:443
2025-08-03 21:19:05,624 - DEBUG - https://api.discogs.com:443 "GET /oauth/identity HTTP/1.1" 429 None
2025-08-03 21:19:05,626 - ERROR - ❌ 账号 1 (<EMAIL>) 初始化失败: 429: You are making requests too quickly.
2025-08-03 21:19:05,628 - DEBUG - Starting new HTTPS connection (1): api.discogs.com:443
2025-08-03 21:19:06,068 - DEBUG - https://api.discogs.com:443 "GET /oauth/identity HTTP/1.1" 429 None
2025-08-03 21:19:06,069 - ERROR - ❌ 账号 2 (<EMAIL>) 初始化失败: 429: You are making requests too quickly.
2025-08-03 21:19:06,069 - ERROR - ❌ 程序执行失败: ❌ 没有可用的Discogs账号
2025-08-03 21:21:29,853 - INFO - ⚠️ 跳过账号 1 的连接测试
2025-08-03 21:21:29,853 - INFO - ✅ 账号 1 (<EMAIL>) 初始化成功
2025-08-03 21:21:31,855 - INFO - ⚠️ 跳过账号 2 的连接测试
2025-08-03 21:21:31,856 - INFO - ✅ 账号 2 (<EMAIL>) 初始化成功
2025-08-03 21:21:31,856 - INFO - 📊 成功初始化 2 个Discogs客户端
2025-08-04 14:45:20,426 - ERROR - ❌ 账号 1 (<EMAIL>) 初始化失败: 401: You must authenticate to access this resource.
2025-08-04 14:45:23,096 - ERROR - ❌ 账号 2 (<EMAIL>) 初始化失败: 401: You must authenticate to access this resource.
2025-08-04 14:45:23,098 - ERROR - ❌ 没有可用的Discogs账号
2025-08-04 14:45:23,098 - ERROR - 💡 可能的原因：
2025-08-04 14:45:23,098 - ERROR -    1. Token无效或过期
2025-08-04 14:45:23,098 - ERROR -    2. API请求过于频繁（429错误）
2025-08-04 14:45:23,098 - ERROR -    3. 网络连接问题
2025-08-04 14:45:23,098 - ERROR -    4. 代理连接失败
2025-08-04 14:45:23,099 - ERROR - 💡 建议：等待几分钟后重试，或检查Token和代理是否有效
2025-08-04 14:45:23,099 - ERROR - ❌ 程序执行失败: ❌ 没有可用的Discogs账号
2025-08-04 14:46:58,084 - ERROR - ❌ 账号 1 (<EMAIL>) 初始化失败: 401: You must authenticate to access this resource.
2025-08-04 14:47:00,828 - ERROR - ❌ 账号 2 (<EMAIL>) 初始化失败: 401: You must authenticate to access this resource.
2025-08-04 14:47:00,828 - ERROR - ❌ 没有可用的Discogs账号
2025-08-04 14:47:00,828 - ERROR - 💡 可能的原因：
2025-08-04 14:47:00,828 - ERROR -    1. Token无效或过期
2025-08-04 14:47:00,828 - ERROR -    2. API请求过于频繁（429错误）
2025-08-04 14:47:00,828 - ERROR -    3. 网络连接问题
2025-08-04 14:47:00,828 - ERROR -    4. 代理连接失败
2025-08-04 14:47:00,828 - ERROR - 💡 建议：等待几分钟后重试，或检查Token和代理是否有效
2025-08-04 14:47:00,828 - ERROR - ❌ 程序执行失败: ❌ 没有可用的Discogs账号
2025-08-04 14:48:33,295 - INFO - ⚠️ 跳过账号 1 的连接测试
2025-08-04 14:48:33,296 - INFO - ✅ 账号 1 (<EMAIL>) (代理: proxy_7897) 初始化成功
2025-08-04 14:48:35,301 - INFO - ⚠️ 跳过账号 2 的连接测试
2025-08-04 14:48:35,304 - INFO - ✅ 账号 2 (<EMAIL>) (代理: proxy_7897) 初始化成功
2025-08-04 14:48:35,304 - INFO - 📊 成功初始化 2 个Discogs客户端
2025-08-04 14:48:35,653 - INFO - ✅ 成功连接到MongoDB数据库: music_test
2025-08-04 14:48:35,654 - INFO - 🚀 开始批量采集Release数据
2025-08-04 14:48:35,654 - INFO - 🔍 开始生成目标ID列表...
2025-08-04 14:48:40,841 - ERROR - ❌ 获取已存在ID失败: Executor error during distinct command :: caused by :: distinct too big, 16mb cap, full error: {'ok': 0.0, 'errmsg': 'Executor error during distinct command :: caused by :: distinct too big, 16mb cap', 'code': 17217, 'codeName': 'Location17217', '$clusterTime': {'clusterTime': Timestamp(1754290120, 14), 'signature': {'hash': b'$\xe0N\xf7\xa3qh\xe0\xfc3\xe54\x96\xd9\xec2\xf4\xedp0', 'keyId': 7495808647054753795}}, 'operationTime': Timestamp(1754290120, 14)}
2025-08-04 14:48:40,842 - INFO - 📊 范围 (100000, 100200) 包含 200 个ID
2025-08-04 14:48:40,843 - INFO - 📊 范围 (200000, 200100) 包含 100 个ID
2025-08-04 14:48:40,843 - INFO - 📊 总ID数: 300
2025-08-04 14:48:40,843 - INFO - 📊 已存在ID数: 0
2025-08-04 14:48:40,843 - INFO - 📊 需要采集ID数: 300
2025-08-04 14:48:40,843 - INFO - 📊 开始采集 300 个Release
2025-08-04 14:48:41,968 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:42,646 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:43,267 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:43,941 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:44,638 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:45,302 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:45,943 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:46,592 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:47,241 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:47,908 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:48,573 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:49,282 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:49,937 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:50,603 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:51,236 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:51,864 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:52,546 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:53,134 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:53,801 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:54,408 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:55,007 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:55,635 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:56,235 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:56,877 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:57,634 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:58,238 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:58,845 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:48:59,502 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:00,181 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:00,791 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:01,412 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:02,553 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:03,206 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:03,873 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:04,575 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:05,188 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:05,891 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:06,751 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:07,394 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:08,171 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:08,797 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:09,407 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:10,076 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:10,672 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:11,346 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:11,966 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:12,553 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:13,165 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:14,827 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:15,465 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:15,465 - INFO - 📊 进度报告: 50/300 (成功: 0, 404: 0, 429: 0, 错误: 50, 成功率: 0.00%, 速度: 8.67/秒)
2025-08-04 14:49:16,096 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:16,743 - ERROR - ❌ 数据转换失败: Expecting value: line 2 column 1 (char 1)
2025-08-04 14:49:16,743 - INFO - 🛑 收到停止信号，终止采集
2025-08-04 14:49:16,743 - INFO - 
============================================================
2025-08-04 14:49:16,743 - INFO - 📊 采集完成 - 最终统计
2025-08-04 14:49:16,743 - INFO - ============================================================
2025-08-04 14:49:16,743 - INFO - 总处理数量: 300
2025-08-04 14:49:16,743 - INFO - 成功获取: 0
2025-08-04 14:49:16,743 - INFO - 404未找到: 0
2025-08-04 14:49:16,743 - INFO - 429限制: 0
2025-08-04 14:49:16,743 - INFO - 其他错误: 52
2025-08-04 14:49:16,743 - INFO - 成功率: 0.00%
2025-08-04 14:49:16,743 - INFO - 处理速度: 8.36 条/秒
2025-08-04 14:49:16,743 - INFO - 总耗时: 35.90 秒
2025-08-04 14:49:16,743 - INFO - ============================================================
2025-08-04 14:49:16,780 - INFO - 📝 数据库连接已关闭

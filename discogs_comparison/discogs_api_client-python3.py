#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs API 多账号多代理数据采集系统

功能：
1. 支持多账号轮换，避免API限制
2. 支持多代理配置，提高请求成功率
3. 智能错误处理和重试机制
4. 基于MongoDB的数据库存储
5. 实时监控和统计功能
6. 用户友好的交互界面

特性：
- 多账号自动轮换
- 多代理智能切换
- 数据库分流存储（正常/404/429错误）
- 实时状态监控
- 优雅停止和断点续传
- 完整的错误处理和日志记录

作者：AI Assistant
创建时间：2025-08-04
"""

import os
import sys
import time
import json
import logging
import random
import re
import signal
from datetime import datetime, timezone
from typing import List, Dict, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

# 第三方库
try:
    import discogs_client
    from discogs_client.exceptions import HTTPError
except ImportError:
    print("❌ 错误：未安装python3-discogs-client库")
    print("请运行：pip install python3-discogs-client")
    sys.exit(1)

try:
    from pymongo import MongoClient
    from pymongo.errors import PyMongoError
except ImportError:
    print("❌ 错误：未安装pymongo库")
    print("请运行：pip install pymongo")
    sys.exit(1)

# 导入现有的枚举类（如果存在）
try:
    from release.enums import Permissions, Status, Source
except ImportError:
    # 如果导入失败，定义本地枚举
    class Permissions:
        """权限枚举类"""
        ALL_VISIBLE = 1

    class Status:
        """状态枚举类"""
        ACTIVE = 1

    class Source:
        """数据源枚举类"""
        DISCOGS = 1

# 全局配置
DEFAULT_CONFIG = {
    'api': {
        'rate_limit': 1.0,      # 1秒1次请求
        'timeout': 30,          # 请求超时时间
        'max_retries': 3,       # 最大重试次数
        'retry_delay': 2.0,     # 重试延迟
    },
    'database': {
        'uri': '**********************************************************',
        'database_name': 'music_test',
        'release_new_collection': 'release_new',
        'release_404_collection': 'release_404',
        'release_copy_collection': 'release_copy',
        'connection_timeout': 30000,
    },
    'logging': {
        'level': 'INFO',
        'format': '%(asctime)s - %(levelname)s - %(message)s',
        'file': 'discogs_api_client.log'
    }
}

# Discogs账号配置
DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'hhKxjHkkcVYSbsWEscUlBekYWLafpUbvzbUGLtPz',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    },
    {
        'email': '<EMAIL>',
        'token': 'zXwURCsYotIjXROUfIngknavKuMWCjcmODCXuEJs',
        'user_agent': 'DiscogsAPIClient/1.0 +https://example.com/contact'
    }
]

# 全局变量
should_stop = False

def signal_handler(signum, frame):
    """信号处理器，用于优雅停止"""
    global should_stop
    should_stop = True
    print("\n🛑 接收到停止信号，正在安全退出...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format=DEFAULT_CONFIG['logging']['format'],
    handlers=[
        logging.FileHandler(DEFAULT_CONFIG['logging']['file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """错误类型枚举"""
    NOT_FOUND = "404"
    RATE_LIMITED = "429"
    NETWORK_ERROR = "network"
    AUTH_ERROR = "auth"
    UNKNOWN_ERROR = "unknown"

@dataclass
class ProxyConfig:
    """代理配置数据类"""
    http: str
    https: str
    name: str

    def to_dict(self) -> Dict[str, str]:
        """转换为字典格式"""
        return {
            'http': self.http,
            'https': self.https
        }

@dataclass
class FetchResult:
    """获取结果数据类"""
    success: bool
    data: Optional[Dict] = None
    error_type: Optional[ErrorType] = None
    error_message: Optional[str] = None
    release_id: Optional[int] = None

@dataclass
class BatchStats:
    """批量处理统计数据类"""
    total: int = 0
    successful: int = 0
    not_found: int = 0
    rate_limited: int = 0
    errors: int = 0
    start_time: float = 0

    def __post_init__(self):
        if self.start_time == 0:
            self.start_time = time.time()

    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time

    @property
    def success_rate(self) -> float:
        return (self.successful / self.total * 100) if self.total > 0 else 0

    @property
    def processing_rate(self) -> float:
        return (self.total / self.elapsed_time) if self.elapsed_time > 0 else 0

# 代理配置
PROXY_CONFIGS = [
    ProxyConfig(
        http='http://127.0.0.1:7897',
        https='http://127.0.0.1:7897',
        name='proxy_7897'
    ),
    ProxyConfig(
        http='http://127.0.0.1:7890',
        https='http://127.0.0.1:7890',
        name='proxy_7890'
    ),
    ProxyConfig(
        http='http://127.0.0.1:1087',
        https='http://127.0.0.1:1087',
        name='proxy_1087'
    ),
]

class ProxyManager:
    """代理管理器 - 处理代理轮换和连接测试"""

    def __init__(self, proxy_configs: List[ProxyConfig]):
        """
        初始化代理管理器

        Args:
            proxy_configs: 代理配置列表
        """
        self.proxy_configs = proxy_configs
        self.current_proxy_index = 0
        self.available_proxies = list(range(len(proxy_configs)))
        self.failed_proxies = set()

    def get_current_proxy(self) -> Optional[ProxyConfig]:
        """
        获取当前可用的代理配置

        Returns:
            ProxyConfig: 当前代理配置，如果没有可用代理则返回None
        """
        if not self.available_proxies:
            logger.warning("⚠️ 没有可用的代理")
            return None

        # 轮换到下一个代理
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.available_proxies)
        proxy_index = self.available_proxies[self.current_proxy_index]

        return self.proxy_configs[proxy_index]

    def mark_proxy_failed(self, proxy_config: ProxyConfig):
        """
        标记代理为失败状态

        Args:
            proxy_config: 失败的代理配置
        """
        for i, config in enumerate(self.proxy_configs):
            if config.name == proxy_config.name:
                if i in self.available_proxies:
                    self.available_proxies.remove(i)
                    self.failed_proxies.add(i)
                    logger.warning(f"⚠️ 代理 {proxy_config.name} 已标记为失败")
                break

    def reset_failed_proxies(self):
        """重置失败的代理，重新加入可用列表"""
        self.available_proxies.extend(self.failed_proxies)
        self.failed_proxies.clear()
        logger.info("🔄 已重置所有失败的代理")

    def get_proxy_info(self, proxy_config: ProxyConfig) -> str:
        """获取代理信息字符串"""
        return f"{proxy_config.name} ({proxy_config.http})"

    def test_proxy_connection(self, proxy_config: ProxyConfig) -> bool:
        """
        测试代理连接

        Args:
            proxy_config: 代理配置

        Returns:
            bool: 连接是否成功
        """
        try:
            import requests
            test_url = "https://httpbin.org/ip"
            response = requests.get(
                test_url,
                proxies=proxy_config.to_dict(),
                timeout=10
            )
            if response.status_code == 200:
                logger.debug(f"✅ 代理 {proxy_config.name} 连接测试成功")
                return True
            else:
                logger.warning(f"⚠️ 代理 {proxy_config.name} 连接测试失败: {response.status_code}")
                return False
        except Exception as e:
            logger.warning(f"⚠️ 代理 {proxy_config.name} 连接测试异常: {e}")
            return False

class DatabaseManager:
    """数据库管理器 - 处理MongoDB连接和操作"""

    def __init__(self, config: Dict = None):
        """
        初始化数据库管理器

        Args:
            config: 数据库配置字典
        """
        self.config = config or DEFAULT_CONFIG['database']
        self.client = None
        self.db = None
        self._connect()

    def _connect(self):
        """连接到MongoDB"""
        try:
            self.client = MongoClient(
                self.config['uri'],
                serverSelectionTimeoutMS=self.config['connection_timeout']
            )
            # 测试连接
            self.client.admin.command('ping')
            self.db = self.client[self.config['database_name']]

            # 确保集合存在
            self._ensure_collections()

            logger.info(f"✅ 成功连接到MongoDB数据库: {self.config['database_name']}")

        except Exception as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            raise

    def _ensure_collections(self):
        """确保所有需要的集合存在"""
        collections = [
            self.config['release_new_collection'],
            self.config['release_404_collection'],
            self.config['release_copy_collection']
        ]

        existing_collections = self.db.list_collection_names()

        for collection_name in collections:
            if collection_name not in existing_collections:
                self.db.create_collection(collection_name)
                logger.info(f"✅ 创建集合: {collection_name}")

    def insert_release_data(self, data: Dict) -> bool:
        """
        插入正常的release数据到release_new表

        Args:
            data: release数据字典

        Returns:
            bool: 插入是否成功
        """
        try:
            collection = self.db[self.config['release_new_collection']]

            # 检查是否已存在
            if collection.find_one({'id': data['id']}):
                logger.debug(f"📝 Release {data['id']} 已存在于数据库")
                return True

            # 插入数据
            collection.insert_one(data)
            logger.debug(f"✅ Release {data['id']} 已插入到 {self.config['release_new_collection']}")
            return True

        except Exception as e:
            logger.error(f"❌ 插入release数据失败 (ID: {data.get('id')}): {e}")
            return False

    def insert_404_record(self, release_id: int) -> bool:
        """
        插入404错误记录到release_404表

        Args:
            release_id: Release ID

        Returns:
            bool: 插入是否成功
        """
        try:
            collection = self.db[self.config['release_404_collection']]

            # 检查是否已存在
            if collection.find_one({'id': release_id}):
                logger.debug(f"📝 404记录 {release_id} 已存在于数据库")
                return True

            # 插入404记录
            doc = {
                'id': release_id,
                'status': 404,
                'timestamp': datetime.now(timezone.utc),
                'source': 'discogs_api_client'
            }
            collection.insert_one(doc)
            logger.debug(f"✅ 404记录 {release_id} 已插入到 {self.config['release_404_collection']}")
            return True

        except Exception as e:
            logger.error(f"❌ 插入404记录失败 (ID: {release_id}): {e}")
            return False

    def insert_error_record(self, release_id: int, status: int) -> bool:
        """
        插入错误记录到release_copy表

        Args:
            release_id: Release ID
            status: 错误状态码

        Returns:
            bool: 插入是否成功
        """
        try:
            collection = self.db[self.config['release_copy_collection']]

            # 检查是否已存在
            if collection.find_one({'id': release_id}):
                logger.debug(f"📝 错误记录 {release_id} 已存在于数据库")
                return True

            # 插入错误记录
            doc = {
                'id': release_id,
                'status': status,
                'timestamp': datetime.now(timezone.utc),
                'source': 'discogs_api_client'
            }
            collection.insert_one(doc)
            logger.debug(f"✅ 错误记录 {release_id} (状态:{status}) 已插入到 {self.config['release_copy_collection']}")
            return True

        except Exception as e:
            logger.error(f"❌ 插入错误记录失败 (ID: {release_id}): {e}")
            return False

    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            logger.info("📝 数据库连接已关闭")

class DiscogsClientManager:
    """Discogs客户端管理器 - 处理多账号轮换和代理集成"""

    def __init__(self, accounts: List[Dict], proxy_manager: ProxyManager = None, test_connection: bool = True):
        """
        初始化客户端管理器

        Args:
            accounts: 账号配置列表
            proxy_manager: 代理管理器
            test_connection: 是否测试连接（默认True）
        """
        self.accounts = accounts
        self.proxy_manager = proxy_manager
        self.current_account_index = 0
        self.clients = {}
        self.last_request_times = {}
        self.test_connection = test_connection
        self._initialize_clients()

    def _initialize_clients(self):
        """初始化所有客户端"""
        for i, account in enumerate(self.accounts):
            try:
                # 获取代理配置
                proxy_config = None
                if self.proxy_manager:
                    proxy_config = self.proxy_manager.get_current_proxy()

                # 创建客户端
                client = self._create_client(account, proxy_config)

                # 添加延迟避免429错误
                if i > 0:
                    time.sleep(2.0)  # 账号间延迟2秒

                # 测试连接（如果启用）
                if self.test_connection:
                    self._test_client_connection(client, i, account)
                else:
                    logger.info(f"⚠️ 跳过账号 {i+1} 的连接测试")

                self.clients[i] = {
                    'client': client,
                    'proxy_config': proxy_config,
                    'account': account
                }
                self.last_request_times[i] = time.time()

                proxy_info = f" (代理: {proxy_config.name})" if proxy_config else " (无代理)"
                logger.info(f"✅ 账号 {i+1} ({account['email']}){proxy_info} 初始化成功")

            except Exception as e:
                logger.error(f"❌ 账号 {i+1} ({account['email']}) 初始化失败: {e}")
                # 如果是429错误，建议等待
                if "429" in str(e):
                    logger.warning(f"💡 建议：账号 {i+1} 遇到频率限制，请稍后再试")

        if not self.clients:
            logger.error("❌ 没有可用的Discogs账号")
            logger.error("💡 可能的原因：")
            logger.error("   1. Token无效或过期")
            logger.error("   2. API请求过于频繁（429错误）")
            logger.error("   3. 网络连接问题")
            logger.error("   4. 代理连接失败")
            logger.error("💡 建议：等待几分钟后重试，或检查Token和代理是否有效")
            raise Exception("❌ 没有可用的Discogs账号")

        logger.info(f"📊 成功初始化 {len(self.clients)} 个Discogs客户端")

    def _create_client(self, account: Dict, proxy_config: ProxyConfig = None):
        """
        创建Discogs客户端

        Args:
            account: 账号配置
            proxy_config: 代理配置

        Returns:
            discogs_client.Client: 客户端实例
        """
        # 注意：python3-discogs-client库不直接支持代理
        # 需要通过设置环境变量或使用requests session
        if proxy_config:
            # 设置代理环境变量
            os.environ['HTTP_PROXY'] = proxy_config.http
            os.environ['HTTPS_PROXY'] = proxy_config.https

        client = discogs_client.Client(
            user_agent=account['user_agent'],
            token=account['token']
        )

        return client

    def _test_client_connection(self, client, index: int, account: Dict):
        """测试客户端连接"""
        max_init_retries = 3
        for attempt in range(max_init_retries):
            try:
                client.identity()
                break
            except HTTPError as e:
                if e.status_code == 429 and attempt < max_init_retries - 1:
                    wait_time = 60 * (attempt + 1)  # 递增等待时间
                    logger.warning(f"⏳ 账号 {index+1} 遇到429错误，等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise

    def get_client(self) -> Tuple[discogs_client.Client, int, Optional[ProxyConfig]]:
        """
        获取当前可用的客户端

        Returns:
            Tuple[客户端实例, 账号索引, 代理配置]
        """
        # 轮换到下一个账号
        available_accounts = list(self.clients.keys())
        if not available_accounts:
            raise Exception("❌ 没有可用的客户端")

        self.current_account_index = (self.current_account_index + 1) % len(available_accounts)
        account_index = available_accounts[self.current_account_index]

        client_info = self.clients[account_index]
        return client_info['client'], account_index, client_info['proxy_config']

    def wait_for_rate_limit(self, account_index: int):
        """等待满足API频率限制"""
        elapsed = time.time() - self.last_request_times.get(account_index, 0)
        if elapsed < DEFAULT_CONFIG['api']['rate_limit']:
            time.sleep(DEFAULT_CONFIG['api']['rate_limit'] - elapsed)
        self.last_request_times[account_index] = time.time()

    def get_account_info(self, account_index: int) -> str:
        """获取账号信息"""
        if account_index in self.clients:
            account = self.clients[account_index]['account']
            proxy_config = self.clients[account_index]['proxy_config']
            proxy_info = f" (代理: {proxy_config.name})" if proxy_config else " (无代理)"
            return f"{account['email']}{proxy_info}"
        return f"账号{account_index + 1}"

    def switch_proxy_for_account(self, account_index: int) -> bool:
        """
        为指定账号切换代理

        Args:
            account_index: 账号索引

        Returns:
            bool: 切换是否成功
        """
        if account_index not in self.clients or not self.proxy_manager:
            return False

        try:
            # 获取新的代理配置
            new_proxy_config = self.proxy_manager.get_current_proxy()
            if not new_proxy_config:
                return False

            # 重新创建客户端
            account = self.clients[account_index]['account']
            new_client = self._create_client(account, new_proxy_config)

            # 更新客户端信息
            self.clients[account_index]['client'] = new_client
            self.clients[account_index]['proxy_config'] = new_proxy_config

            logger.info(f"🔄 账号 {account_index+1} 已切换到代理: {new_proxy_config.name}")
            return True

        except Exception as e:
            logger.error(f"❌ 账号 {account_index+1} 切换代理失败: {e}")
            return False
